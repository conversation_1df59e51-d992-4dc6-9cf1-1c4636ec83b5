/* Product Search Component Styles */
.product-search {
  position: relative;
  width: 100%;
  max-width: 300px;
}

.product-search .search-bar {
  display: flex;
  overflow: hidden;
  border-radius: 0.375rem;
  width: 100%;
}

.product-search .search-input {
  background-color: #323232;
  padding: 0.5rem 1.25rem;
  color: #b7b7b7;
  border: none;
  width: 100%;
  font-size: 0.875rem;
}

.product-search .search-input:focus {
  outline: none;
  background-color: #404040;
}

.product-search .search-input::placeholder {
  color: #888;
}

.product-search .search-btn {
  background-color: #ffcd29;
  padding: 0.25rem 0.75rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.product-search .search-btn:hover {
  background-color: #e6b800;
}

.product-search .search-btn i {
  color: black;
  font-size: 1.25rem;
}

/* Search Dropdown */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  margin-top: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.search-results {
  padding: 0.5rem 0;
}

/* Search Result Items */
.search-result-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-bottom: 1px solid #f3f4f6;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover,
.search-result-item.focused {
  background-color: #f8f9fa;
}

.result-image {
  width: 50px;
  height: 50px;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.25rem;
  border: 1px solid #e1e5e9;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.result-brand {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 0.5rem;
}

.result-price {
  font-weight: 500;
  color: #059669;
  white-space: nowrap;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.loading-state i {
  margin-right: 0.5rem;
}

/* No Results State */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  text-align: center;
}

.no-results i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

/* Error State */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  color: #dc2626;
  font-size: 0.875rem;
}

.error-state i {
  margin-right: 0.5rem;
}

/* Open State */
.product-search.open .search-input {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-search {
    max-width: 250px;
  }
  
  .search-dropdown {
    left: -50px;
    right: -50px;
  }
  
  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .result-brand {
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .product-search {
    max-width: 200px;
  }
  
  .search-dropdown {
    left: -100px;
    right: -100px;
  }
  
  .result-image {
    width: 40px;
    height: 40px;
    margin-right: 0.5rem;
  }
  
  .search-result-item {
    padding: 0.5rem 0.75rem;
  }
}
