.topNavigationBar {
  display: flex;
  background-color: black;
  padding: 1rem 1.25rem;
  width: 100%;
}

.navigationBarContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 1.5rem 0 1.5rem;
  gap: 20px;
  width: 100%;
}

.logoNavigationBar {
  width: 40px;
  height: 40px;
}

.topNavigationBar nav {
  padding: 0 0 0 200px;
  display: none;
}

.logo-image{
  width: 40px;
  height: 40px;
}

.search-bar {
  display: flex;
  overflow: hidden;
  border-radius: 0.375rem;
  width: 100%;
  max-width: 300px;
}

.search-bar input {
  background-color: #323232;
  padding: 0.5rem 1.25rem;
  color: #b7b7b7;
  border: none;
  width: 100%;
}

.search-bar input:focus {
  outline: none;
}

.search-bar button {
  background-color: #ffcd29;
  padding: 0.25rem 0.75rem;
  border: none;
  cursor: pointer;
}

.search-bar button i {
  color: black;
  font-size: 1.25rem;
}

@media (min-width: 1280px) {
  .topNavigationBar {
    display: flex;
    align-items: center;
  }

  .topNavigationBar nav {
    padding: 0 0 0 200px;
    display: block;
  }

  .topNavigationBar .nav-links {
    display: flex;
    gap: 2.5rem;
    align-items: center;
  }

  .topNavigationBar .nav-links a {
    color: #838383;
    text-decoration: none;
    transition: color 0.3s;
  }

  .topNavigationBar .nav-links a.active {
    color: #f8be00;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 700px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1140px;
  }
}