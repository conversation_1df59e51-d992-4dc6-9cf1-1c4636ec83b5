.filter-container {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-row-2col {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-group {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: bold;
  font-size: 14px;
  color: #313131;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-sub {
  padding: 0.375rem 0.75rem;
  margin-right: -10px;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem 0 0 0.25rem;
}

.filter-input {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  line-height: 1;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0 0.25rem 0.25rem 0;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.filter-input-status {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  line-height: 1;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.filter-sub {
  width: 50px;
  font-size: 10px;
  font-weight: 500;
  background-color: #f0f0f0;
  padding: 5.6px 8px;
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  box-sizing: border-box;
}

.divider {
border-top: 1px solid #e0e0e0;
margin: 20px 0;
}

.card-container {
display: grid;
justify-content: center;
grid-template-columns: repeat(2, 1fr);
gap: 15px;
max-width: 1200px;
width: 100%;
padding: 1rem;
margin: 0 auto;
box-sizing: border-box;
margin-bottom: 5rem;
}
        
.card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.card-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.card-content {
    padding: 12px;
}

.product-name {
    font-size: 14px;
    font-weight: normal;
    margin-bottom: 8px;
    color: #333;
}

.product-price {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
}

.product-rating {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;
}

.stars {
    color: #FFD700;
    margin-right: 3px;
}

.sold-count {
    margin-left: 5px;
}

@media (min-width: 768px) {
.card-container {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.product-name {
    font-size: 16px;
}
}